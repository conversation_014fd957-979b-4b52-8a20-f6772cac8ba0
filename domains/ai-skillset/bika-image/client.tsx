import { type GeneratedImage, ImagesArtifact } from '../../ai-artifacts/images-artifact';
import type { SkillsetUIMap } from '../types';
import { ImageSkillsetName } from './type';

const ImageToolsetUI: SkillsetUIMap = {
  [ImageSkillsetName.generate_image]: {
    artifact: ({ toolInvocation, skillsets }) => {
      if (toolInvocation.state === 'input-streaming') {
        return null;
      }
      if (toolInvocation?.output == null) {
        return null;
      }
      return (
        <ImagesArtifact
          dataList={(toolInvocation?.output ?? []) as GeneratedImage[]}
          skillsets={skillsets}
          content={JSON.stringify(toolInvocation?.output)}
          tool={toolInvocation}
        />
      );
    },
  },
};

export default ImageToolsetUI;
